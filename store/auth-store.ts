import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { User } from "@/types";

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => void;
}

// Mock user for demo purposes
const mockUser: User = {
  id: "user1",
  name: "<PERSON>",
  email: "<EMAIL>",
  photoURL: "https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80",
  createdAt: Date.now()
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      
      login: async (email, password) => {
        set({ isLoading: true });
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real app, this would validate credentials with Firebase
        set({ 
          user: mockUser,
          isAuthenticated: true,
          isLoading: false
        });
      },
      
      register: async (name, email, password) => {
        set({ isLoading: true });
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real app, this would create a new user in Firebase
        const newUser: User = {
          ...mockUser,
          name,
          email,
          createdAt: Date.now()
        };
        
        set({ 
          user: newUser,
          isAuthenticated: true,
          isLoading: false
        });
      },
      
      logout: () => {
        set({ 
          user: null,
          isAuthenticated: false
        });
      },
      
      updateProfile: (data) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...data } : null
        }));
      }
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => AsyncStorage)
    }
  )
);