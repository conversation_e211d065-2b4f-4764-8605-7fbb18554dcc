import React from "react";
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator,
  ViewStyle,
  TextStyle
} from "react-native";
import { colors } from "@/constants/colors";

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline" | "text";
  size?: "small" | "medium" | "large";
  isLoading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = "primary",
  size = "medium",
  isLoading = false,
  disabled = false,
  style,
  textStyle,
  leftIcon,
  rightIcon
}) => {
  const getButtonStyles = () => {
    const baseStyle: ViewStyle = {
      ...styles.button,
      ...sizeStyles[size]
    };

    if (disabled) {
      return {
        ...baseStyle,
        ...variantStyles[variant].disabled
      };
    }

    return {
      ...baseStyle,
      ...variantStyles[variant].container
    };
  };

  const getTextStyles = () => {
    if (disabled) {
      return {
        ...styles.text,
        ...variantStyles[variant].disabledText
      };
    }

    return {
      ...styles.text,
      ...variantStyles[variant].text,
      ...sizeTextStyles[size]
    };
  };

  return (
    <TouchableOpacity
      style={[getButtonStyles(), style]}
      onPress={onPress}
      disabled={disabled || isLoading}
      activeOpacity={0.8}
    >
      {isLoading ? (
        <ActivityIndicator 
          color={variant === "primary" ? colors.white : colors.primary} 
          size="small" 
        />
      ) : (
        <>
          {leftIcon && <>{leftIcon}</>}
          <Text style={[getTextStyles(), textStyle]}>{title}</Text>
          {rightIcon && <>{rightIcon}</>}
        </>
      )}
    </TouchableOpacity>
  );
};

const variantStyles = {
  primary: {
    container: {
      backgroundColor: colors.primary,
      borderWidth: 0,
    },
    text: {
      color: colors.white,
    },
    disabled: {
      backgroundColor: colors.gray[300],
      borderWidth: 0,
    },
    disabledText: {
      color: colors.gray[500],
    },
  },
  secondary: {
    container: {
      backgroundColor: colors.secondary,
      borderWidth: 0,
    },
    text: {
      color: colors.white,
    },
    disabled: {
      backgroundColor: colors.gray[300],
      borderWidth: 0,
    },
    disabledText: {
      color: colors.gray[500],
    },
  },
  outline: {
    container: {
      backgroundColor: "transparent",
      borderWidth: 1,
      borderColor: colors.primary,
    },
    text: {
      color: colors.primary,
    },
    disabled: {
      backgroundColor: "transparent",
      borderWidth: 1,
      borderColor: colors.gray[300],
    },
    disabledText: {
      color: colors.gray[500],
    },
  },
  text: {
    container: {
      backgroundColor: "transparent",
      borderWidth: 0,
      paddingHorizontal: 8,
    },
    text: {
      color: colors.primary,
    },
    disabled: {
      backgroundColor: "transparent",
      borderWidth: 0,
    },
    disabledText: {
      color: colors.gray[500],
    },
  },
};

const sizeStyles = {
  small: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  medium: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  large: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 10,
  },
};

const sizeTextStyles = {
  small: {
    fontSize: 14,
  },
  medium: {
    fontSize: 16,
  },
  large: {
    fontSize: 18,
  },
};

const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  text: {
    fontWeight: "600",
    textAlign: "center",
  },
});