export type User = {
    id: string;
    name: string;
    email: string;
    photoURL?: string;
    createdAt: number;
  };
  
  export type LocationFeature = 
    | "parallel_parking" 
    | "three_point_turn" 
    | "roundabout" 
    | "highway_merging" 
    | "traffic_lights" 
    | "stop_signs" 
    | "school_zone" 
    | "residential" 
    | "downtown" 
    | "hill_parking" 
    | "quiet_streets";
  
  export type DifficultyLevel = "beginner" | "intermediate" | "advanced";
  
  export type Location = {
    id: string;
    name: string;
    address: string;
    description: string;
    latitude: number;
    longitude: number;
    features: LocationFeature[];
    difficulty: DifficultyLevel;
    createdBy: string;
    createdAt: number;
    averageRating: number;
    reviewCount: number;
  };
  
  export type Review = {
    id: string;
    locationId: string;
    userId: string;
    userName: string;
    userPhotoURL?: string;
    rating: number;
    comment: string;
    createdAt: number;
  };