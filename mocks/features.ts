import { LocationFeature } from "@/types";

export const featureLabels: Record<LocationFeature, string> = {
  parallel_parking: "Parallel Parking",
  three_point_turn: "Three-Point Turn",
  roundabout: "Roundabout",
  highway_merging: "Highway Merging",
  traffic_lights: "Traffic Lights",
  stop_signs: "Stop Signs",
  school_zone: "School Zone",
  residential: "Residential",
  downtown: "Downtown",
  hill_parking: "Hill Parking",
  quiet_streets: "Quiet Streets"
};

export const featureIcons: Record<LocationFeature, string> = {
  parallel_parking: "car",
  three_point_turn: "rotate-ccw",
  roundabout: "circle",
  highway_merging: "git-merge",
  traffic_lights: "traffic-cone",
  stop_signs: "octagon",
  school_zone: "school",
  residential: "home",
  downtown: "building",
  hill_parking: "mountain",
  quiet_streets: "volume-x"
};