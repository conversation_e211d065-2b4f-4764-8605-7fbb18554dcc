import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Location, LocationFeature, DifficultyLevel, Review } from "@/types";
import { mockLocations } from "@/mocks/locations";
import { mockReviews } from "@/mocks/reviews";

interface LocationState {
  locations: Location[];
  reviews: Review[];
  selectedLocation: Location | null;
  isLoading: boolean;
  activeFilters: {
    features: LocationFeature[];
    difficulty: DifficultyLevel | null;
    searchQuery: string;
  };
  
  // Actions
  fetchLocations: () => Promise<void>;
  fetchLocationById: (id: string) => Promise<Location | null>;
  fetchReviewsByLocationId: (locationId: string) => Promise<Review[]>;
  addLocation: (location: Omit<Location, "id" | "createdAt" | "averageRating" | "reviewCount">) => Promise<Location>;
  addReview: (review: Omit<Review, "id" | "createdAt">) => Promise<Review>;
  setSelectedLocation: (location: Location | null) => void;
  setFilters: (filters: Partial<LocationState["activeFilters"]>) => void;
  clearFilters: () => void;
}

export const useLocationStore = create<LocationState>()(
  persist(
    (set, get) => ({
      locations: mockLocations,
      reviews: mockReviews,
      selectedLocation: null,
      isLoading: false,
      activeFilters: {
        features: [],
        difficulty: null,
        searchQuery: "",
      },
      
      fetchLocations: async () => {
        set({ isLoading: true });
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // In a real app, this would fetch from Firebase
        set({ 
          locations: mockLocations,
          isLoading: false
        });
      },
      
      fetchLocationById: async (id) => {
        set({ isLoading: true });
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // In a real app, this would fetch from Firebase
        const location = mockLocations.find(loc => loc.id === id) || null;
        set({ isLoading: false });
        
        return location;
      },
      
      fetchReviewsByLocationId: async (locationId) => {
        // In a real app, this would fetch from Firebase
        const locationReviews = mockReviews.filter(review => review.locationId === locationId);
        return locationReviews;
      },
      
      addLocation: async (locationData) => {
        set({ isLoading: true });
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real app, this would add to Firebase
        const newLocation: Location = {
          id: `loc_${Date.now()}`,
          ...locationData,
          createdAt: Date.now(),
          averageRating: 0,
          reviewCount: 0
        };
        
        set(state => ({
          locations: [...state.locations, newLocation],
          isLoading: false
        }));
        
        return newLocation;
      },
      
      addReview: async (reviewData) => {
        set({ isLoading: true });
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // In a real app, this would add to Firebase
        const newReview: Review = {
          id: `rev_${Date.now()}`,
          ...reviewData,
          createdAt: Date.now()
        };
        
        // Update reviews array
        set(state => ({
          reviews: [...state.reviews, newReview],
          isLoading: false
        }));
        
        // Update location's average rating and review count
        const { locations } = get();
        const locationIndex = locations.findIndex(loc => loc.id === reviewData.locationId);
        
        if (locationIndex !== -1) {
          const location = locations[locationIndex];
          const locationReviews = [...get().reviews.filter(r => r.locationId === location.id), newReview];
          
          const totalRating = locationReviews.reduce((sum, review) => sum + review.rating, 0);
          const averageRating = totalRating / locationReviews.length;
          
          const updatedLocation = {
            ...location,
            averageRating,
            reviewCount: locationReviews.length
          };
          
          const updatedLocations = [...locations];
          updatedLocations[locationIndex] = updatedLocation;
          
          set({ locations: updatedLocations });
        }
        
        return newReview;
      },
      
      setSelectedLocation: (location) => {
        set({ selectedLocation: location });
      },
      
      setFilters: (filters) => {
        set(state => ({
          activeFilters: {
            ...state.activeFilters,
            ...filters
          }
        }));
      },
      
      clearFilters: () => {
        set({
          activeFilters: {
            features: [],
            difficulty: null,
            searchQuery: ""
          }
        });
      }
    }),
    {
      name: "location-storage",
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        // Only persist these fields
        locations: state.locations,
        reviews: state.reviews
      })
    }
  )
);