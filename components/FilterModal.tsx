import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Pressable,
} from "react-native";
import { colors } from "@/constants/colors";
import { Button } from "./Button";
import { X, Check } from "lucide-react-native";
import { LocationFeature, DifficultyLevel } from "@/types";
import { featureLabels } from "@/mocks/features";

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: {
    features: LocationFeature[];
    difficulty: DifficultyLevel | null;
  }) => void;
  initialFilters: {
    features: LocationFeature[];
    difficulty: DifficultyLevel | null;
  };
}

export const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  onClose,
  onApply,
  initialFilters,
}) => {
  const [selectedFeatures, setSelectedFeatures] = useState<LocationFeature[]>(
    initialFilters.features || []
  );
  const [selectedDifficulty, setSelectedDifficulty] = useState<DifficultyLevel | null>(
    initialFilters.difficulty || null
  );

  useEffect(() => {
    if (visible) {
      setSelectedFeatures(initialFilters.features || []);
      setSelectedDifficulty(initialFilters.difficulty || null);
    }
  }, [visible, initialFilters]);

  const handleFeatureToggle = (feature: LocationFeature) => {
    if (selectedFeatures.includes(feature)) {
      setSelectedFeatures(selectedFeatures.filter((f) => f !== feature));
    } else {
      setSelectedFeatures([...selectedFeatures, feature]);
    }
  };

  const handleDifficultySelect = (difficulty: DifficultyLevel | null) => {
    setSelectedDifficulty(difficulty);
  };

  const handleApply = () => {
    onApply({
      features: selectedFeatures,
      difficulty: selectedDifficulty,
    });
    onClose();
  };

  const handleReset = () => {
    setSelectedFeatures([]);
    setSelectedDifficulty(null);
  };

  const allFeatures: LocationFeature[] = [
    "parallel_parking",
    "three_point_turn",
    "roundabout",
    "highway_merging",
    "traffic_lights",
    "stop_signs",
    "school_zone",
    "residential",
    "downtown",
    "hill_parking",
    "quiet_streets",
  ];

  const difficultyLevels: (DifficultyLevel | null)[] = [
    null,
    "beginner",
    "intermediate",
    "advanced",
  ];

  const difficultyLabels: Record<string, string> = {
    null: "All Levels",
    beginner: "Beginner",
    intermediate: "Intermediate",
    advanced: "Advanced",
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filter Locations</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={colors.gray[700]} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Features</Text>
              <View style={styles.featureGrid}>
                {allFeatures.map((feature) => (
                  <TouchableOpacity
                    key={feature}
                    style={[
                      styles.featureItem,
                      selectedFeatures.includes(feature) && styles.featureItemSelected,
                    ]}
                    onPress={() => handleFeatureToggle(feature)}
                  >
                    <Text
                      style={[
                        styles.featureText,
                        selectedFeatures.includes(feature) && styles.featureTextSelected,
                      ]}
                    >
                      {featureLabels[feature]}
                    </Text>
                    {selectedFeatures.includes(feature) && (
                      <Check size={16} color={colors.white} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Difficulty Level</Text>
              <View style={styles.difficultyContainer}>
                {difficultyLevels.map((difficulty) => (
                  <Pressable
                    key={String(difficulty)}
                    style={[
                      styles.difficultyItem,
                      selectedDifficulty === difficulty && {
                        backgroundColor:
                          difficulty === null
                            ? colors.primary
                            : colors.difficulty[difficulty as DifficultyLevel] || colors.primary,
                      },
                    ]}
                    onPress={() => handleDifficultySelect(difficulty)}
                  >
                    <Text
                      style={[
                        styles.difficultyText,
                        selectedDifficulty === difficulty && styles.difficultyTextSelected,
                      ]}
                    >
                      {difficultyLabels[String(difficulty)]}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <Button
              title="Reset"
              variant="outline"
              onPress={handleReset}
              style={{ flex: 1 }}
            />
            <Button
              title="Apply Filters"
              onPress={handleApply}
              style={{ flex: 2 }}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
    color: colors.text,
  },
  featureGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.gray[100],
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  featureItemSelected: {
    backgroundColor: colors.primary,
  },
  featureText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  featureTextSelected: {
    color: colors.white,
  },
  difficultyContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  difficultyItem: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
  },
  difficultyText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.gray[700],
  },
  difficultyTextSelected: {
    color: colors.white,
  },
  modalFooter: {
    flexDirection: "row",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    gap: 12,
  },
});