import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "@/constants/colors";
import { useLocationStore } from "@/store/location-store";
import { useAuthStore } from "@/store/auth-store";
import { Input } from "@/components/Input";
import { Button } from "@/components/Button";
import { LocationFeature, DifficultyLevel } from "@/types";
import { featureLabels } from "@/mocks/features";
import { MapPin, Check, Navigation } from "lucide-react-native";

export default function AddLocationScreen() {
  const router = useRouter();
  const { addLocation, isLoading } = useLocationStore();
  const { user, isAuthenticated } = useAuthStore();

  const [name, setName] = useState("");
  const [address, setAddress] = useState("");
  const [description, setDescription] = useState("");
  const [selectedFeatures, setSelectedFeatures] = useState<LocationFeature[]>([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState<DifficultyLevel>("beginner");
  const [errors, setErrors] = useState<{
    name?: string;
    address?: string;
    description?: string;
    features?: string;
  }>({});

  // Mock coordinates for demo purposes
  const [coordinates, setCoordinates] = useState({
    latitude: 43.6532,
    longitude: -79.3832,
  });

  const validate = () => {
    const newErrors: {
      name?: string;
      address?: string;
      description?: string;
      features?: string;
    } = {};

    if (!name.trim()) {
      newErrors.name = "Location name is required";
    }

    if (!address.trim()) {
      newErrors.address = "Address is required";
    }

    if (!description.trim()) {
      newErrors.description = "Description is required";
    }

    if (selectedFeatures.length === 0) {
      newErrors.features = "Select at least one feature";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!isAuthenticated) {
      router.push("/(auth)/login");
      return;
    }

    if (validate()) {
      try {
        const newLocation = await addLocation({
          name,
          address,
          description,
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          features: selectedFeatures,
          difficulty: selectedDifficulty,
          createdBy: user?.id || "anonymous",
        });

        router.push(`/location/${newLocation.id}`);
      } catch (error) {
        console.error("Error adding location:", error);
      }
    }
  };

  const toggleFeature = (feature: LocationFeature) => {
    if (selectedFeatures.includes(feature)) {
      setSelectedFeatures(selectedFeatures.filter((f) => f !== feature));
    } else {
      setSelectedFeatures([...selectedFeatures, feature]);
    }
  };

  const allFeatures: LocationFeature[] = [
    "parallel_parking",
    "three_point_turn",
    "roundabout",
    "highway_merging",
    "traffic_lights",
    "stop_signs",
    "school_zone",
    "residential",
    "downtown",
    "hill_parking",
    "quiet_streets",
  ];

  return (
    <SafeAreaView style={styles.container} edges={["bottom"]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Location Details</Text>
            <Input
              label="Location Name"
              placeholder="E.g., Centennial Park Parking Lot"
              value={name}
              onChangeText={setName}
              error={errors.name}
            />

            <Input
              label="Address"
              placeholder="Full address of the location"
              value={address}
              onChangeText={setAddress}
              error={errors.address}
            />

            <Input
              label="Description"
              placeholder="Describe what makes this a good practice location..."
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              error={errors.description}
            />
          </View>

          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Location on Map</Text>
            <View style={styles.mapContainer}>
              <View style={styles.mockMap}>
                <MapPin size={32} color={colors.primary} />
                <Text style={styles.mockMapText}>
                  Tap to select location on map
                </Text>
              </View>
              <TouchableOpacity style={styles.useCurrentLocation}>
                <Navigation size={16} color={colors.primary} />
                <Text style={styles.useCurrentLocationText}>
                  Use Current Location
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Features</Text>
            <Text style={styles.sectionSubtitle}>
              Select all features available at this location
            </Text>
            {errors.features && (
              <Text style={styles.errorText}>{errors.features}</Text>
            )}
            <View style={styles.featuresGrid}>
              {allFeatures.map((feature) => (
                <TouchableOpacity
                  key={feature}
                  style={[
                    styles.featureItem,
                    selectedFeatures.includes(feature) && styles.featureItemSelected,
                  ]}
                  onPress={() => toggleFeature(feature)}
                >
                  <Text
                    style={[
                      styles.featureText,
                      selectedFeatures.includes(feature) && styles.featureTextSelected,
                    ]}
                  >
                    {featureLabels[feature]}
                  </Text>
                  {selectedFeatures.includes(feature) && (
                    <Check size={16} color={colors.white} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Difficulty Level</Text>
            <Text style={styles.sectionSubtitle}>
              How challenging is this location for new drivers?
            </Text>
            <View style={styles.difficultyContainer}>
              {(["beginner", "intermediate", "advanced"] as DifficultyLevel[]).map(
                (difficulty) => (
                  <TouchableOpacity
                    key={difficulty}
                    style={[
                      styles.difficultyItem,
                      selectedDifficulty === difficulty && {
                        backgroundColor: colors.difficulty[difficulty],
                      },
                    ]}
                    onPress={() => setSelectedDifficulty(difficulty)}
                  >
                    <Text
                      style={[
                        styles.difficultyText,
                        selectedDifficulty === difficulty && styles.difficultyTextSelected,
                      ]}
                    >
                      {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                    </Text>
                  </TouchableOpacity>
                )
              )}
            </View>
          </View>

          <View style={styles.submitContainer}>
            <Button
              title="Add Location"
              onPress={handleSubmit}
              isLoading={isLoading}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 16,
  },
  mapContainer: {
    marginBottom: 8,
  },
  mockMap: {
    height: 180,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  mockMapText: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 8,
  },
  useCurrentLocation: {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "flex-end",
    gap: 4,
  },
  useCurrentLocationText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
  },
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.gray[100],
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
    marginBottom: 8,
  },
  featureItemSelected: {
    backgroundColor: colors.primary,
  },
  featureText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  featureTextSelected: {
    color: colors.white,
  },
  difficultyContainer: {
    flexDirection: "row",
    gap: 12,
  },
  difficultyItem: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
    alignItems: "center",
  },
  difficultyText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.gray[700],
  },
  difficultyTextSelected: {
    color: colors.white,
  },
  errorText: {
    color: colors.danger,
    fontSize: 14,
    marginBottom: 8,
  },
  submitContainer: {
    marginTop: 16,
    marginBottom: 32,
  },
});