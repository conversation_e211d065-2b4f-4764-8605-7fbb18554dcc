import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Location } from "@/types";
import { colors } from "@/constants/colors";
import { MapPin, Star, Clock } from "lucide-react-native";
import { FeatureTag } from "./FeatureTag";

interface LocationCardProps {
  location: Location;
  onPress: (location: Location) => void;
}

export const LocationCard: React.FC<LocationCardProps> = ({
  location,
  onPress,
}) => {
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return colors.difficulty.beginner;
      case "intermediate":
        return colors.difficulty.intermediate;
      case "advanced":
        return colors.difficulty.advanced;
      default:
        return colors.gray[500];
    }
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress(location)}
      activeOpacity={0.7}
    >
      <View style={styles.header}>
        <Text style={styles.name}>{location.name}</Text>
        <View style={styles.ratingContainer}>
          <Star size={16} color={colors.warning} fill={colors.warning} />
          <Text style={styles.rating}>
            {location.averageRating.toFixed(1)} ({location.reviewCount})
          </Text>
        </View>
      </View>

      <View style={styles.addressContainer}>
        <MapPin size={16} color={colors.gray[600]} />
        <Text style={styles.address} numberOfLines={1}>
          {location.address}
        </Text>
      </View>

      <View style={styles.featuresContainer}>
        {location.features.slice(0, 3).map((feature, index) => (
          <FeatureTag key={index} feature={feature} size="small" />
        ))}
        {location.features.length > 3 && (
          <View style={styles.moreFeatures}>
            <Text style={styles.moreFeaturesText}>
              +{location.features.length - 3}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.footer}>
        <View
          style={[
            styles.difficultyBadge,
            { backgroundColor: getDifficultyColor(location.difficulty) + "20" },
          ]}
        >
          <Text
            style={[
              styles.difficultyText,
              { color: getDifficultyColor(location.difficulty) },
            ]}
          >
            {location.difficulty.charAt(0).toUpperCase() +
              location.difficulty.slice(1)}
          </Text>
        </View>

        <View style={styles.dateContainer}>
          <Clock size={14} color={colors.gray[500]} />
          <Text style={styles.date}>{formatDate(location.createdAt)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  name: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    flex: 1,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  rating: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.text,
  },
  addressContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    gap: 6,
  },
  address: {
    fontSize: 14,
    color: colors.gray[600],
    flex: 1,
  },
  featuresContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 12,
  },
  moreFeatures: {
    backgroundColor: colors.gray[200],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  moreFeaturesText: {
    fontSize: 12,
    color: colors.gray[700],
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  difficultyBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 4,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: "500",
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  date: {
    fontSize: 12,
    color: colors.gray[500],
  },
});