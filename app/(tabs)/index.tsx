import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "@/constants/colors";
import { useLocationStore } from "@/store/location-store";
import { useAuthStore } from "@/store/auth-store";
import { LocationCard } from "@/components/LocationCard";
import { Input } from "@/components/Input";
import { EmptyState } from "@/components/EmptyState";
import { FilterModal } from "@/components/FilterModal";
import { Location, LocationFeature, DifficultyLevel } from "@/types";
import { Search, Filter, MapPin, LogIn } from "lucide-react-native";

export default function HomeScreen() {
  const router = useRouter();
  const { isAuthenticated, user } = useAuthStore();
  const {
    locations,
    isLoading,
    fetchLocations,
    setSelectedLocation,
    activeFilters,
    setFilters,
  } = useLocationStore();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilterModal, setShowFilterModal] = useState(false);

  useEffect(() => {
    fetchLocations();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchLocations();
    setRefreshing(false);
  };

  const handleLocationPress = (location: Location) => {
    setSelectedLocation(location);
    router.push(`/location/${location.id}`);
  };

  const handleApplyFilters = (filters: {
    features: LocationFeature[];
    difficulty: DifficultyLevel | null;
  }) => {
    setFilters(filters);
  };

  const filteredLocations = locations.filter((location) => {
    // Filter by search query
    const matchesSearch =
      searchQuery === "" ||
      location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by features
    const matchesFeatures =
      activeFilters.features.length === 0 ||
      activeFilters.features.every((feature) =>
        location.features.includes(feature)
      );

    // Filter by difficulty
    const matchesDifficulty =
      activeFilters.difficulty === null ||
      location.difficulty === activeFilters.difficulty;

    return matchesSearch && matchesFeatures && matchesDifficulty;
  });

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.greeting}>
        {isAuthenticated
          ? `Hello, ${user?.name?.split(" ")[0] || "Driver"}!`
          : "Hello, Driver!"}
      </Text>
      <Text style={styles.subtitle}>Find the perfect spot to practice driving</Text>

      <View style={styles.searchContainer}>
        <Input
          placeholder="Search locations..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Search size={20} color={colors.gray[500]} />}
          rightIcon={
            <TouchableOpacity onPress={() => setShowFilterModal(true)}>
              <Filter size={20} color={colors.gray[600]} />
            </TouchableOpacity>
          }
          style={styles.searchInput}
        />
      </View>

      {(activeFilters.features.length > 0 || activeFilters.difficulty !== null) && (
        <View style={styles.activeFiltersContainer}>
          <Text style={styles.activeFiltersText}>Active filters:</Text>
          <TouchableOpacity
            onPress={() => setShowFilterModal(true)}
            style={styles.viewFiltersButton}
          >
            <Text style={styles.viewFiltersText}>View/Edit</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  if (isLoading && !refreshing && locations.length === 0) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <FlatList
        data={filteredLocations}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <LocationCard location={item} onPress={handleLocationPress} />
        )}
        contentContainerStyle={styles.listContent}
        ListHeaderComponent={renderHeader}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <EmptyState
            title="No locations found"
            description={
              activeFilters.features.length > 0 || activeFilters.difficulty !== null
                ? "Try adjusting your filters or search query"
                : "Be the first to add a practice location!"
            }
            icon={<MapPin size={48} color={colors.gray[400]} />}
            actionLabel={
              isAuthenticated ? "Add New Location" : "Sign In to Add Location"
            }
            onAction={() =>
              isAuthenticated
                ? router.push("/location/add")
                : router.push("/(auth)/login")
            }
          />
        }
      />

      <FilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={handleApplyFilters}
        initialFilters={{
          features: activeFilters.features,
          difficulty: activeFilters.difficulty,
        }}
      />

      {!isAuthenticated && (
        <TouchableOpacity
          style={styles.loginButton}
          onPress={() => router.push("/(auth)/login")}
        >
          <LogIn size={20} color={colors.white} />
          <Text style={styles.loginButtonText}>Sign In</Text>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[100],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.gray[100],
  },
  header: {
    padding: 16,
    backgroundColor: colors.white,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    marginBottom: 16,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 2,
  },
  greeting: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 16,
  },
  searchContainer: {
    marginBottom: 8,
  },
  searchInput: {
    marginBottom: 0,
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  activeFiltersContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  activeFiltersText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  viewFiltersButton: {
    marginLeft: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: colors.primaryLight,
    borderRadius: 4,
  },
  viewFiltersText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: "500",
  },
  loginButton: {
    position: "absolute",
    bottom: 20,
    right: 20,
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  loginButtonText: {
    color: colors.white,
    fontWeight: "600",
    fontSize: 14,
  },
});