import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "@/constants/colors";
import { useLocationStore } from "@/store/location-store";
import { Location } from "@/types";
import { MapPin, Navigation, List, Filter } from "lucide-react-native";
import { FilterModal } from "@/components/FilterModal";
import { EmptyState } from "@/components/EmptyState";
import MapView, { Marker } from "react-native-maps";

// Mock map component since we can't use actual maps
const MockMap = ({ locations, onLocationSelect }: { 
  locations: Location[],
  onLocationSelect: (location: Location) => void
}) => {
  return (
    <View style={styles.mockMap}>
      <Text style={styles.mockMapText}>Interactive Map</Text>
      <Text style={styles.mockMapSubtext}>
        {locations.length} locations available
      </Text>
      <View style={styles.mockPins}>
        {locations.slice(0, 5).map((location, index) => (
          <TouchableOpacity
            key={location.id}
            style={[
              styles.mockPin,
              {
                top: 50 + Math.random() * 150,
                left: 30 + (index * 60) % (Dimensions.get("window").width - 100),
              },
            ]}
            onPress={() => onLocationSelect(location)}
          >
            <MapPin
              size={28}
              color={
                location.difficulty === "beginner"
                  ? colors.difficulty.beginner
                  : location.difficulty === "intermediate"
                  ? colors.difficulty.intermediate
                  : colors.difficulty.advanced
              }
              fill={
                location.difficulty === "beginner"
                  ? colors.difficulty.beginner
                  : location.difficulty === "intermediate"
                  ? colors.difficulty.intermediate
                  : colors.difficulty.advanced
              }
            />
            <View style={styles.mockPinLabel}>
              <Text style={styles.mockPinText} numberOfLines={1}>
                {location.name}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default function MapScreen() {
  const router = useRouter();
  const {
    locations,
    setSelectedLocation,
    activeFilters,
    setFilters,
  } = useLocationStore();

  const [showFilterModal, setShowFilterModal] = useState(false);
  const [userLocation, setUserLocation] = useState({
    latitude: 43.6532,
    longitude: -79.3832,
  });

  const filteredLocations = locations.filter((location) => {
    // Filter by features
    const matchesFeatures =
      activeFilters.features.length === 0 ||
      activeFilters.features.every((feature) =>
        location.features.includes(feature)
      );

    // Filter by difficulty
    const matchesDifficulty =
      activeFilters.difficulty === null ||
      location.difficulty === activeFilters.difficulty;

    return matchesFeatures && matchesDifficulty;
  });

  const handleLocationSelect = (location: Location) => {
    setSelectedLocation(location);
    router.push(`/location/${location.id}`);
  };

  const handleListView = () => {
    router.push("/");
  };

  if (Platform.OS === "web") {
    return (
      <SafeAreaView style={styles.container} edges={["top"]}>
        <View style={styles.header}>
          <Text style={styles.title}>Explore Locations</Text>
          <View style={styles.headerButtons}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => setShowFilterModal(true)}
            >
              <Filter size={20} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleListView}
            >
              <List size={20} color={colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        <EmptyState
          title="Map View"
          description="Maps are not available in web view. Please use the mobile app for the full experience."
          icon={<MapPin size={48} color={colors.gray[400]} />}
          actionLabel="Go to List View"
          onAction={handleListView}
        />

        <FilterModal
          visible={showFilterModal}
          onClose={() => setShowFilterModal(false)}
          onApply={setFilters}
          initialFilters={{
            features: activeFilters.features,
            difficulty: activeFilters.difficulty,
          }}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <View style={styles.header}>
        <Text style={styles.title}>Explore Locations</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowFilterModal(true)}
          >
            <Filter size={20} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleListView}
          >
            <List size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: 43.6532,
            longitude: -79.3832,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}
          showsUserLocation
        >
          {filteredLocations.map((location) => (
            <Marker
              key={location.id}
              coordinate={{
                latitude: location.latitude,
                longitude: location.longitude,
              }}
              title={location.name}
              description={`Difficulty: ${location.difficulty}`}
              onPress={() => handleLocationSelect(location)}
            />
          ))}
        </MapView>
      </View>

      <TouchableOpacity style={styles.currentLocationButton}>
        <Navigation size={24} color={colors.primary} />
      </TouchableOpacity>

      <FilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={setFilters}
        initialFilters={{
          features: activeFilters.features,
          difficulty: activeFilters.difficulty,
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[100],
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: colors.white,
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.text,
  },
  headerButtons: {
    flexDirection: "row",
    gap: 12,
  },
  headerButton: {
    padding: 8,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
  },
  mockMap: {
    flex: 1,
    backgroundColor: "#E8EDF1",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  mockMapText: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.gray[600],
  },
  mockMapSubtext: {
    fontSize: 16,
    color: colors.gray[500],
    marginTop: 8,
  },
  mockPins: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  mockPin: {
    position: "absolute",
    alignItems: "center",
  },
  mockPinLabel: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginTop: 4,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  mockPinText: {
    fontSize: 12,
    fontWeight: "500",
    color: colors.text,
    maxWidth: 100,
  },
  currentLocationButton: {
    position: "absolute",
    bottom: 24,
    right: 16,
    backgroundColor: colors.white,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  mapContainer: {
    flex: 1,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: "hidden",
  },
  map: {
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height,
  },
});