{"expo": {"name": "expo-app", "slug": "expo-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": false, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.breezydriver.breezydriver", "config": {"googleMapsApiKey": "AIzaSyC1RjVDqDZ1Judxqs7ZljIAIMkKqCFVlOQ"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "config": {"googleMaps": {"apiKey": "AIzaSyC1RjVDqDZ1Judxqs7ZljIAIMkKqCFVlOQ"}}, "package": "com.breezydriver.breezydriver"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true, "reactNativeNewArchitecture": false}}}