import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { colors } from "@/constants/colors";
import { LocationFeature } from "@/types";
import { featureLabels } from "@/mocks/features";

interface FeatureTagProps {
  feature: LocationFeature;
  size?: "small" | "medium" | "large";
}

export const FeatureTag: React.FC<FeatureTagProps> = ({
  feature,
  size = "medium",
}) => {
  const getStyles = () => {
    switch (size) {
      case "small":
        return {
          container: {
            paddingHorizontal: 8,
            paddingVertical: 4,
            borderRadius: 4,
          },
          text: {
            fontSize: 12,
          },
        };
      case "large":
        return {
          container: {
            paddingHorizontal: 12,
            paddingVertical: 8,
            borderRadius: 8,
          },
          text: {
            fontSize: 16,
          },
        };
      default:
        return {
          container: {
            paddingHorizontal: 10,
            paddingVertical: 6,
            borderRadius: 6,
          },
          text: {
            fontSize: 14,
          },
        };
    }
  };

  const sizeStyles = getStyles();

  return (
    <View
      style={[
        styles.container,
        sizeStyles.container,
        { backgroundColor: colors.primaryLight },
      ]}
    >
      <Text style={[styles.text, sizeStyles.text, { color: colors.primary }]}>
        {featureLabels[feature]}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.primaryLight,
  },
  text: {
    fontWeight: "500",
  },
});