import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "@/constants/colors";
import { useLocationStore } from "@/store/location-store";
import { useAuthStore } from "@/store/auth-store";
import { Button } from "@/components/Button";
import { ReviewCard } from "@/components/ReviewCard";
import { FeatureTag } from "@/components/FeatureTag";
import { Review } from "@/types";
import {
  MapPin,
  Star,
  Clock,
  ChevronRight,
  Share2,
  Heart,
  Navigation,
  MessageSquare,
} from "lucide-react-native";
import { featureLabels } from "@/mocks/features";

// Mock map component since we can't use actual maps
const MockLocationMap = ({ latitude, longitude }: { latitude: number; longitude: number }) => {
  return (
    <View style={styles.mockMap}>
      <MapPin size={32} color={colors.primary} />
      <Text style={styles.mockMapText}>Map View</Text>
      <Text style={styles.mockMapCoords}>
        {latitude.toFixed(4)}, {longitude.toFixed(4)}
      </Text>
    </View>
  );
};

export default function LocationDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const {
    locations,
    reviews,
    isLoading,
    fetchLocationById,
    fetchReviewsByLocationId,
    selectedLocation,
  } = useLocationStore();

  const [locationReviews, setLocationReviews] = useState<Review[]>([]);

  useEffect(() => {
    const loadLocation = async () => {
      if (id) {
        const location = await fetchLocationById(id);
        if (location) {
          const reviews = await fetchReviewsByLocationId(id);
          setLocationReviews(reviews);
        }
      }
    };

    loadLocation();
  }, [id]);

  const location = selectedLocation || locations.find((loc) => loc.id === id);

  if (isLoading || !location) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return colors.difficulty.beginner;
      case "intermediate":
        return colors.difficulty.intermediate;
      case "advanced":
        return colors.difficulty.advanced;
      default:
        return colors.gray[500];
    }
  };

  const handleAddReview = () => {
    if (isAuthenticated) {
      router.push(`/location/review/${location.id}`);
    } else {
      router.push("/(auth)/login");
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={["bottom"]}>
      <ScrollView>
        {Platform.OS !== "web" ? (
          <MockLocationMap
            latitude={location.latitude}
            longitude={location.longitude}
          />
        ) : (
          <View style={styles.webMapPlaceholder}>
            <MapPin size={32} color={colors.primary} />
            <Text style={styles.webMapText}>
              Map view not available on web
            </Text>
          </View>
        )}

        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>{location.name}</Text>
            <View style={styles.ratingContainer}>
              <Star size={18} color={colors.warning} fill={colors.warning} />
              <Text style={styles.rating}>
                {location.averageRating.toFixed(1)} ({location.reviewCount} reviews)
              </Text>
            </View>
          </View>

          <View style={styles.addressContainer}>
            <MapPin size={18} color={colors.gray[600]} />
            <Text style={styles.address}>{location.address}</Text>
          </View>

          <View style={styles.actionsContainer}>
            <TouchableOpacity style={styles.actionButton}>
              <Navigation size={20} color={colors.primary} />
              <Text style={styles.actionText}>Directions</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Heart size={20} color={colors.primary} />
              <Text style={styles.actionText}>Save</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Share2 size={20} color={colors.primary} />
              <Text style={styles.actionText}>Share</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{location.description}</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Features</Text>
            <View style={styles.featuresContainer}>
              {location.features.map((feature, index) => (
                <FeatureTag key={index} feature={feature} />
              ))}
            </View>
          </View>

          <View style={styles.infoContainer}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Difficulty</Text>
              <View
                style={[
                  styles.difficultyBadge,
                  {
                    backgroundColor:
                      getDifficultyColor(location.difficulty) + "20",
                  },
                ]}
              >
                <Text
                  style={[
                    styles.difficultyText,
                    { color: getDifficultyColor(location.difficulty) },
                  ]}
                >
                  {location.difficulty.charAt(0).toUpperCase() +
                    location.difficulty.slice(1)}
                </Text>
              </View>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Added On</Text>
              <Text style={styles.infoValue}>
                {formatDate(location.createdAt)}
              </Text>
            </View>
          </View>

          <View style={styles.reviewsSection}>
            <View style={styles.reviewsHeader}>
              <Text style={styles.sectionTitle}>Reviews</Text>
              <TouchableOpacity>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>

            {locationReviews.length > 0 ? (
              <View style={styles.reviewsList}>
                {locationReviews.slice(0, 3).map((review) => (
                  <ReviewCard key={review.id} review={review} />
                ))}
              </View>
            ) : (
              <View style={styles.noReviews}>
                <MessageSquare size={32} color={colors.gray[400]} />
                <Text style={styles.noReviewsText}>No reviews yet</Text>
                <Text style={styles.noReviewsSubtext}>
                  Be the first to share your experience
                </Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Add Review"
          onPress={handleAddReview}
          leftIcon={<Star size={20} color={colors.white} />}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
  },
  mockMap: {
    height: 200,
    backgroundColor: "#E8EDF1",
    justifyContent: "center",
    alignItems: "center",
  },
  mockMapText: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.gray[600],
    marginTop: 8,
  },
  mockMapCoords: {
    fontSize: 14,
    color: colors.gray[500],
    marginTop: 4,
  },
  webMapPlaceholder: {
    height: 200,
    backgroundColor: "#E8EDF1",
    justifyContent: "center",
    alignItems: "center",
  },
  webMapText: {
    fontSize: 16,
    color: colors.gray[600],
    marginTop: 8,
  },
  content: {
    padding: 16,
  },
  header: {
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  rating: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
  },
  addressContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
    gap: 8,
  },
  address: {
    fontSize: 16,
    color: colors.gray[600],
    flex: 1,
    lineHeight: 22,
  },
  actionsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.gray[200],
  },
  actionButton: {
    alignItems: "center",
    gap: 8,
  },
  actionText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
  },
  featuresContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  infoContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 16,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.text,
  },
  difficultyBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: "flex-start",
  },
  difficultyText: {
    fontSize: 14,
    fontWeight: "500",
  },
  reviewsSection: {
    marginBottom: 24,
  },
  reviewsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  seeAllText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
  },
  reviewsList: {
    gap: 12,
  },
  noReviews: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
  },
  noReviewsText: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginTop: 12,
    marginBottom: 4,
  },
  noReviewsSubtext: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: "center",
  },
  footer: {
    padding: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
});