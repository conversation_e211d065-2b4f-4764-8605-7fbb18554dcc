import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "@/constants/colors";
import { useAuthStore } from "@/store/auth-store";
import { useLocationStore } from "@/store/location-store";
import { Button } from "@/components/Button";
import { LocationCard } from "@/components/LocationCard";
import { EmptyState } from "@/components/EmptyState";
import {
  User,
  LogOut,
  MapPin,
  Settings,
  Star,
  Clock,
  ChevronRight,
} from "lucide-react-native";

export default function ProfileScreen() {
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuthStore();
  const { locations, setSelectedLocation } = useLocationStore();

  // Filter locations created by the current user
  const userLocations = locations.filter(
    (location) => isAuthenticated && location.createdBy === user?.id
  );

  const handleLocationPress = (location: any) => {
    setSelectedLocation(location);
    router.push(`/location/${location.id}`);
  };

  const handleLogin = () => {
    router.push("/(auth)/login");
  };

  const handleLogout = () => {
    logout();
  };

  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container} edges={["top"]}>
        <EmptyState
          title="Sign in to view your profile"
          description="Create an account to save your favorite locations and contribute to the community"
          icon={<User size={64} color={colors.gray[400]} />}
          actionLabel="Sign In"
          onAction={handleLogin}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView>
        <View style={styles.header}>
          <View style={styles.profileInfo}>
            <Image
              source={{
                uri: user?.photoURL || "https://images.unsplash.com/photo-*************-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80",
              }}
              style={styles.avatar}
            />
            <View>
              <Text style={styles.name}>{user?.name}</Text>
              <Text style={styles.email}>{user?.email}</Text>
            </View>
          </View>

          <TouchableOpacity style={styles.settingsButton}>
            <Settings size={20} color={colors.gray[600]} />
          </TouchableOpacity>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <MapPin size={20} color={colors.primary} />
            <Text style={styles.statValue}>{userLocations.length}</Text>
            <Text style={styles.statLabel}>Locations</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Star size={20} color={colors.warning} />
            <Text style={styles.statValue}>4.7</Text>
            <Text style={styles.statLabel}>Avg. Rating</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Clock size={20} color={colors.info} />
            <Text style={styles.statValue}>
              {Math.floor((Date.now() - (user?.createdAt || 0)) / (1000 * 60 * 60 * 24))}
            </Text>
            <Text style={styles.statLabel}>Days</Text>
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Locations</Text>
            {userLocations.length > 0 && (
              <TouchableOpacity>
                <Text style={styles.sectionAction}>View All</Text>
              </TouchableOpacity>
            )}
          </View>

          {userLocations.length > 0 ? (
            <View style={styles.locationsList}>
              {userLocations.slice(0, 3).map((location) => (
                <LocationCard
                  key={location.id}
                  location={location}
                  onPress={handleLocationPress}
                />
              ))}
            </View>
          ) : (
            <View style={styles.emptyLocations}>
              <MapPin size={32} color={colors.gray[400]} />
              <Text style={styles.emptyTitle}>No locations yet</Text>
              <Text style={styles.emptyDescription}>
                Share your favorite practice spots with the community
              </Text>
              <Button
                title="Add Location"
                onPress={() => router.push("/location/add")}
                style={styles.addButton}
                size="small"
              />
            </View>
          )}
        </View>

        <View style={styles.menuSection}>
          <TouchableOpacity style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <View style={[styles.menuIcon, { backgroundColor: colors.primaryLight }]}>
                <Star size={20} color={colors.primary} />
              </View>
              <Text style={styles.menuText}>Favorite Locations</Text>
            </View>
            <ChevronRight size={20} color={colors.gray[400]} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <View style={[styles.menuIcon, { backgroundColor: colors.secondaryLight }]}>
                <Clock size={20} color={colors.secondary} />
              </View>
              <Text style={styles.menuText}>Recent Activity</Text>
            </View>
            <ChevronRight size={20} color={colors.gray[400]} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <View style={[styles.menuIcon, { backgroundColor: "#E8F4FF" }]}>
                <Settings size={20} color="#0080FF" />
              </View>
              <Text style={styles.menuText}>Account Settings</Text>
            </View>
            <ChevronRight size={20} color={colors.gray[400]} />
          </TouchableOpacity>
        </View>

        <Button
          title="Log Out"
          variant="outline"
          onPress={handleLogout}
          leftIcon={<LogOut size={20} color={colors.primary} />}
          style={styles.logoutButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[100],
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: colors.white,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 2,
  },
  profileInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  name: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  email: {
    fontSize: 14,
    color: colors.gray[600],
  },
  settingsButton: {
    padding: 8,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
  },
  statsContainer: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: "center",
    gap: 4,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[600],
  },
  statDivider: {
    width: 1,
    height: "80%",
    backgroundColor: colors.gray[200],
  },
  section: {
    marginTop: 24,
    marginHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  sectionAction: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "500",
  },
  locationsList: {
    gap: 12,
  },
  emptyLocations: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
    marginTop: 12,
    marginBottom: 4,
  },
  emptyDescription: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: "center",
    marginBottom: 16,
  },
  addButton: {
    minWidth: 120,
  },
  menuSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 24,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  menuText: {
    fontSize: 16,
    color: colors.text,
  },
  logoutButton: {
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 32,
  },
});