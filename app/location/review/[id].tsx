import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "@/constants/colors";
import { useLocationStore } from "@/store/location-store";
import { useAuthStore } from "@/store/auth-store";
import { Input } from "@/components/Input";
import { Button } from "@/components/Button";
import { Star } from "lucide-react-native";

export default function AddReviewScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { locations, fetchLocationById, addReview, isLoading } = useLocationStore();
  const { user, isAuthenticated } = useAuthStore();

  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState("");
  const [errors, setErrors] = useState<{ rating?: string; comment?: string }>({});
  const [location, setLocation] = useState(
    locations.find((loc) => loc.id === id)
  );

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace("/(auth)/login");
      return;
    }

    const loadLocation = async () => {
      if (id) {
        const locationData = await fetchLocationById(id);
        if (locationData) {
          setLocation(locationData);
        }
      }
    };

    if (!location) {
      loadLocation();
    }
  }, [id, isAuthenticated]);

  const validate = () => {
    const newErrors: { rating?: string; comment?: string } = {};

    if (rating === 0) {
      newErrors.rating = "Please select a rating";
    }

    if (!comment.trim()) {
      newErrors.comment = "Please provide a comment";
    } else if (comment.length < 10) {
      newErrors.comment = "Comment must be at least 10 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!isAuthenticated || !user) {
      router.replace("/(auth)/login");
      return;
    }

    if (validate()) {
      try {
        await addReview({
          locationId: id || "",
          userId: user.id,
          userName: user.name,
          userPhotoURL: user.photoURL,
          rating,
          comment,
        });

        router.replace(`/location/${id}`);
      } catch (error) {
        console.error("Error adding review:", error);
      }
    }
  };

  if (!location) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading location...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={["bottom"]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.locationInfo}>
            <Text style={styles.locationName}>{location.name}</Text>
            <Text style={styles.locationAddress}>{location.address}</Text>
          </View>

          <View style={styles.ratingSection}>
            <Text style={styles.sectionTitle}>Your Rating</Text>
            {errors.rating && (
              <Text style={styles.errorText}>{errors.rating}</Text>
            )}
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <TouchableOpacity
                  key={star}
                  onPress={() => setRating(star)}
                  style={styles.starButton}
                >
                  <Star
                    size={36}
                    color={colors.warning}
                    fill={star <= rating ? colors.warning : "transparent"}
                  />
                </TouchableOpacity>
              ))}
            </View>
            <Text style={styles.ratingText}>
              {rating === 0
                ? "Tap to rate"
                : rating === 1
                ? "Poor"
                : rating === 2
                ? "Fair"
                : rating === 3
                ? "Good"
                : rating === 4
                ? "Very Good"
                : "Excellent"}
            </Text>
          </View>

          <View style={styles.commentSection}>
            <Text style={styles.sectionTitle}>Your Review</Text>
            <Input
              placeholder="Share your experience with this location..."
              value={comment}
              onChangeText={setComment}
              multiline
              numberOfLines={6}
              error={errors.comment}
            />
          </View>

          <View style={styles.tipSection}>
            <Text style={styles.tipTitle}>Tips for a helpful review:</Text>
            <Text style={styles.tipText}>
              • Mention specific features you practiced
            </Text>
            <Text style={styles.tipText}>
              • Note the best times to visit
            </Text>
            <Text style={styles.tipText}>
              • Share any challenges or benefits for new drivers
            </Text>
          </View>

          <Button
            title="Submit Review"
            onPress={handleSubmit}
            isLoading={isLoading}
            style={styles.submitButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  locationInfo: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: colors.gray[100],
    borderRadius: 12,
  },
  locationName: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 14,
    color: colors.gray[600],
  },
  ratingSection: {
    marginBottom: 24,
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: 16,
    alignSelf: "flex-start",
  },
  starsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 8,
  },
  starButton: {
    padding: 8,
  },
  ratingText: {
    fontSize: 16,
    color: colors.text,
    marginTop: 8,
  },
  commentSection: {
    marginBottom: 24,
  },
  tipSection: {
    backgroundColor: colors.primaryLight,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.primary,
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 4,
  },
  errorText: {
    color: colors.danger,
    fontSize: 14,
    marginBottom: 8,
    alignSelf: "flex-start",
  },
  submitButton: {
    marginBottom: 32,
  },
});