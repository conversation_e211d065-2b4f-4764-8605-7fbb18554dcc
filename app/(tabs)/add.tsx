import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "@/constants/colors";
import { Button } from "@/components/Button";
import { MapPin, Plus } from "lucide-react-native";

// This is a placeholder screen that redirects to the add location screen
export default function AddScreen() {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <MapPin size={64} color={colors.primary} />
        <Text style={styles.title}>Add New Location</Text>
        <Text style={styles.description}>
          Share your favorite practice spots with other G1 drivers
        </Text>
        <Button
          title="Add Location"
          onPress={() => router.push("/location/add")}
          leftIcon={<Plus size={20} color={colors.white} />}
          style={styles.button}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: "center",
    marginBottom: 32,
  },
  button: {
    minWidth: 200,
  },
});